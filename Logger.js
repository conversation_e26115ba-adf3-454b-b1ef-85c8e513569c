const fs = require('fs');
const path = require('path');
const moment = require('moment');
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class Logger {
  constructor() {
    ipcRenderer.invoke('get-app-path').then((appDataPath) => {
      const currentDate = moment().format("YYYY-MM-DD");
      const logDir = path.join(appDataPath, 'sewpos');

      // Dizin yoksa oluştur
      this.ensureDirectoryExists(logDir);

      this.logFilePath = path.join(logDir, `log-${currentDate}.json`);
      console.log(`Log dosyası yolu: ${this.logFilePath}`);
    }).catch(err => {
      console.error('Log dizini oluşturma hatası:', err);
    });
  }

  // Dizin yoksa oluştur
  ensureDirectoryExists(dirPath) {
    try {
      if (!fs.existsSync(dirPath)) {
        console.log(`Dizin oluşturuluyor: ${dirPath}`);
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Dizin oluşturuldu: ${dirPath}`);
      }
    } catch (err) {
      console.error(`Dizin oluşturma hatası: ${dirPath}`, err);
    }
  }

  log(text) {
    try {
      const separator = "=".repeat(50);
      const logText = `${moment().format("YYYY-MM-DD HH:mm:ss")} => ${text}`;

      if (!this.logFilePath) {
        console.error("logFilePath henüz tanımlanmadı.");
        return;
      }

      // Dizin kontrolü
      const logDir = path.dirname(this.logFilePath);
      this.ensureDirectoryExists(logDir);

      let fileContent = "";
      if (fs.existsSync(this.logFilePath)) {
        fileContent = fs.readFileSync(this.logFilePath, "utf8");
      }

      fs.writeFileSync(this.logFilePath, `${logText}
${separator}
${fileContent}`);

      console.log(`Log yazıldı: ${text}`);
    } catch (err) {
      console.error('Log yazma hatası:', err);
    }
  }

  getLogContent(date) {
    const logDate = date ? date : moment().format("YYYY-MM-DD");
    return ipcRenderer.invoke('get-app-path').then((appDataPath) => {
      const logDir = path.join(appDataPath, 'sewpos');
      const logFilePath = path.join(logDir, `log-${logDate}.json`);

      // Dizin kontrolü
      this.ensureDirectoryExists(logDir);

      if (!fs.existsSync(logFilePath)) {
        console.log(`Log dosyası bulunamadı: ${logFilePath}`);
        return [];
      }

      try {
        const fileContent = fs.readFileSync(logFilePath, "utf8");
        return fileContent.split(`
${"=".repeat(50)}
`);
      } catch (err) {
        console.error(`Log okuma hatası: ${logFilePath}`, err);
        return [];
      }
    }).catch(err => {
      console.error('Log içeriği alma hatası:', err);
      return [];
    });
  }

  clear(date) {
    const logDate = date ? date : moment().format("YYYY-MM-DD");
    return ipcRenderer.invoke('get-app-path').then((appDataPath) => {
      const logDir = path.join(appDataPath, 'sewpos');
      const logFilePath = path.join(logDir, `log-${logDate}.json`);

      // Dizin kontrolü
      this.ensureDirectoryExists(logDir);

      try {
        if (fs.existsSync(logFilePath)) {
          fs.writeFileSync(logFilePath, "");
          console.log(`Log dosyası temizlendi: ${logFilePath}`);
        } else {
          console.log(`Temizlenecek log dosyası bulunamadı: ${logFilePath}`);
        }
      } catch (err) {
        console.error(`Log temizleme hatası: ${logFilePath}`, err);
      }
    }).catch(err => {
      console.error('Log temizleme hatası:', err);
    });
  }
}

module.exports = Logger;
