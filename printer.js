const thermalPrinter = require('node-thermal-printer');
const fastXmlParser = require('fast-xml-parser');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { AppConfig } = require('./config/app');
const jimp = require('jimp');
const puppeteer = require('puppeteer');
const Logger = require('./services/Logger');

class XMLParser {
  constructor(logger = new Logger.Logger()) {
    this.logger = logger;
    this.config = { interface: "" };
    this.fileCreated = [];
  }

  getTagValue(tagName, parent) {
    const result = parent.find(tag => tagName === Object.keys(tag)[0]);
    return result ? result[tagName][0]["#text"] || false : false;
  }

  static async deleteAllTemporaryFiles() {
    const directory = AppConfig.paths.storage;
    fs.readdir(directory, (err, files) => {
      if (err) throw err;
      files.forEach(file => {
        fs.unlink(path.join(directory, file), err => {
          if (err) throw err;
        });
      });
    });
  }

  async print(xml) {
    this.fileCreated = [];
    const options = new Options();

    if (options.get("free_version")) {
      xml = this.injectAds(xml);
    }

    const parsingOptions = {
      ignoreAttributes: false,
      attributeNamePrefix: "@",
      alwaysCreateTextNode: false,
      attributesGroupName: "attributes",
      allowBooleanAttributes: true,
      preserveOrder: true
    };

    let result;
    try {
      result = new fastXmlParser.XMLParser(parsingOptions).parse(xml);
    } catch (error) {
      this.logger.log("An error occurred while parsing the XML document.");
      return { status: "error", message: error.toString() };
    }

    const document = result.filter(tag => tag.document !== void 0);
    if (document.length === 0) {
      return { status: "error", message: "Invalid Print Job Document" };
    }

    const config = this.getConfig(result);
    this.config = config;

    this.thermalPrinter = new thermalPrinter.printer(this.config);

    try {
      await this.walker(document[0].document);
      await this.cleanupFiles();
      await this.thermalPrinter.execute();
      return { status: "success", message: "Printed" };
    } catch (error) {
      this.logger.log("An error occurred while executing the print command on the printer");
      return { status: "error", message: error.toString() };
    }
  }

  getConfig(result) {
    const configData = result[1].configuration;
    const characterSet = this.getTagValue("characterset", configData);
    const printerInterface = this.getTagValue("interface", configData);
    const lineCharacter = this.getTagValue("line-character", configData);
    const type = this.getTagValue("type", configData);

    return {
      characterSet: characterSet || "",
      interface: printerInterface,
      lineCharacter,
      type: type || thermalPrinter.types.EPSON,
      driver: require('@thiagoelg/node-printer'),
      removeSpecialCharacters: configData[0]["remove-special-chars"] || false
    };
  }

  async cleanupFiles() {
    for (const file of this.fileCreated) {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    }
  }

  generateRandomString(length) {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    return Array.from({ length }, () => characters.charAt(Math.floor(Math.random() * characters.length))).join('');
  }

  injectAds(xmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlString, "text/xml");
    const targetNode = doc.getElementsByTagName("paper-cut")[0] || doc.getElementsByTagName("full-cut")[0] || doc.getElementsByTagName("partial-cut")[0] || doc.getElementsByTagName("document")[0];

    const adElement = doc.createElement("align");
    adElement.setAttribute("mode", "center");

    const adText = ["Printed Using NexoPOS & Nexo Print Server", "my.nexopos.com"];
    adText.forEach(text => {
      const textElement = doc.createElement("text-line");
      textElement.textContent = text;
      adElement.appendChild(textElement);
    });

    targetNode.parentNode.insertBefore(adElement, targetNode);
    return doc.toString();
  }

  async downloadImage(url) {
    const fileName = `image${this.generateRandomString(20)}`;
    const extension = path.extname(url);
    let filePath = path.join(AppConfig.paths.storage, `${fileName}${extension}`);

    try {
      const response = await axios.get(url, { responseType: "stream" });
      const imageStream = fs.createWriteStream(filePath);
      await new Promise((resolve, reject) => {
        response.data.pipe(imageStream)
          .on("error", reject)
          .once("close", () => resolve(filePath));
      });

      if (extension !== ".png") {
        const newPath = path.join(AppConfig.paths.storage, `${fileName}.png`);
        await jimp.read(filePath).then(image => image.writeAsync(newPath));
        filePath = newPath;
      }

      await this.thermalPrinter.printImage(filePath);
      this.fileCreated.push(filePath);
    } catch (error) {
      this.logger.log(`Error downloading image: ${error}`);
    }
  }

  async walker(result) {
    for (const tag of result) {
      const currentTagName = Object.keys(tag)[0];
      const attrParentName = Object.keys(tag)[1];

      switch (currentTagName) {
        case "align":
          this.setDirection(tag[attrParentName].attributes["@mode"]);
          await this.walker(tag[currentTagName]);
          this.resetDirection();
          break;

        case "font":
          this.setFontType(tag[attrParentName].attributes["@type"]);
          await this.walker(tag[currentTagName]);
          this.setFontType("a");
          break;

        case "double-height":
          this.thermalPrinter.setTextDoubleHeight();
          await this.walker(tag[currentTagName]);
          this.thermalPrinter.setTextNormal();
          break;

        case "double-width":
          this.thermalPrinter.setTextDoubleWidth();
          await this.walker(tag[currentTagName]);
          this.thermalPrinter.setTextNormal();
          break;

        case "space-between":
          this.thermalPrinter.leftRight(tag[currentTagName][0].text[0]["#text"], tag[currentTagName][1].text[0]["#text"]);
          break;

        case "table-row":
          this.renderTable(tag[currentTagName]);
          break;

        case "quad-size":
          this.thermalPrinter.setTextQuadArea();
          await this.walker(tag[currentTagName]);
          this.thermalPrinter.setTextNormal();
          break;

        case "web-url":
          await this.capture(tag[currentTagName][0]["#text"]);
          break;

        case "bold":
          this.thermalPrinter.bold(true);
          typeof tag[currentTagName][0]["#text"] === "string" ? this.thermalPrinter.println(tag[currentTagName][0]["#text"]) : await this.walker(tag[currentTagName]);
          this.thermalPrinter.bold(false);
          break;

        case "invert":
          this.thermalPrinter.invert(true);
          typeof tag[currentTagName][0]["#text"] === "string" ? this.thermalPrinter.println(tag[currentTagName][0]["#text"]) : await this.walker(tag[currentTagName]);
          this.thermalPrinter.invert(false);
          break;

        case "text-line":
          if (tag[currentTagName][0]) {
            this.thermalPrinter.println(tag[currentTagName][0]["#text"] || "");
          }
          break;

        case "image":
          await this.downloadImage(tag[currentTagName][0]["#text"] || "");
          break;

        case "text":
          this.thermalPrinter.setTextNormal();
          await this.walker(tag[currentTagName]);
          this.thermalPrinter.setTextNormal();
          break;

        case "line-separator":
          this.thermalPrinter.drawLine();
          break;

        case "line-feed":
          this.thermalPrinter.newLine();
          break;

        case "cash-drawer":
          this.thermalPrinter.openCashDrawer();
          break;

        case "full-cut":
        case "partial-cut":
          this.thermalPrinter.cut();
          break;

        case "qr-code":
          this.thermalPrinter.printQR(tag[currentTagName][0]["#text"] || 0, { cellSize: tag[attrParentName].attributes["@size"] || 8 });
          break;

        case "base64":
          const base64Data = tag[currentTagName][0]["#text"];
          const dataBuffer = Buffer.from(base64Data, "base64");
          const filePath = path.join(AppConfig.paths.storage, "base64-image.png");
          fs.writeFileSync(filePath, dataBuffer);
          await this.thermalPrinter.printImage(filePath);
          break;

        case "beep":
          this.thermalPrinter.beep();
          break;

        default:
          break;
      }
    }
  }

  async capture(url) {
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      await page.goto(url);
      await page.waitForTimeout(3000); // Optional wait for elements to load
      const screenshotPath = path.join(AppConfig.paths.storage, `screenshot-${this.generateRandomString(20)}.png`);
      await page.screenshot({ path: screenshotPath });
      await this.thermalPrinter.printImage(screenshotPath);
      await browser.close();
      this.fileCreated.push(screenshotPath);
    } catch (error) {
      this.logger.log(`Error capturing web page: ${error}`);
    }
  }

  renderTable(tableData) {
    // Create a table structure for printing
    const formattedRows = tableData.map(row => {
        return {
            text: row.text,
            align: row.align || 'left', // Default to left alignment
            width: row.width || 0.33, // Default column width
            cols: row.cols || undefined, // Optional column count
            bold: row.bold || false // Optional bold setting
        };
    });
    this.thermalPrinter.tableCustom(formattedRows); // Use the thermal printer's method to render the table
    }
    
    setDirection(direction) {
        // Set the printer alignment based on the specified direction
        switch (direction) {
            case 'center':
                this.thermalPrinter.alignCenter();
                break;
            case 'left':
                this.thermalPrinter.alignLeft();
                break;
            case 'right':
                this.thermalPrinter.alignRight();
                break;
            default:
                this.thermalPrinter.alignLeft(); // Default to left if invalid direction
                break;
        }
    }
    
    resetDirection() {
        // Reset the printer alignment to left
        this.thermalPrinter.alignLeft();
    }
    
    setFontType(type) {
        // Set the font type for printing based on the specified type
        switch (type) {
            case 'a':
                this.thermalPrinter.setTypeFontA();
                break;
            case 'b':
                this.thermalPrinter.setTypeFontB();
                break;
            default:
                this.thermalPrinter.setTypeFontA(); // Default to font A if invalid type
                break;
        }
    }
    
}

module.exports = XMLParser;
