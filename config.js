const fs = require('fs');
const path = require('path');
const { app } = require('electron');


const configPath = path.join(app.getPath('appData'), 'config.json');

function ensureConfigExists() {
    if (!fs.existsSync(configPath)) {
        writeConfig({ tenant_id: null, accessToken: null, storeName: null });
    }
}

function readConfig() {
    ensureConfigExists(); // Dosya yoksa oluştur
    try {
        const data = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error("Hatalı JSON dosyası:", error);
        return { tenant_id: null, accessToken: null, storeName: null }; // Bozuksa varsayılan değer döndür
    }
}

function writeConfig(data) {
    if (typeof data !== "object" || data === null) {
        console.error("Geçersiz config verisi:", data);
        return;
    }
    fs.writeFileSync(configPath, JSON.stringify(data, null, 2));
}

// ✅ **Yeniden giriş yapıldığında `config.json`'u sıfırla ve tekrar oluştur**
function resetConfig() {
    if (fs.existsSync(configPath)) {
        fs.unlinkSync(configPath); // Eski dosyayı sil
    }
    ensureConfigExists(); // Yeni dosyayı oluştur
}

// **Config içine yeni verileri kaydet**
function setConfig(tenant_id, accessToken, storeName) {
    resetConfig(); // Önce eski config dosyasını sil ve yeni oluştur
    writeConfig({ tenant_id, accessToken, storeName });
}

// **Config verisini temizle (Çıkış yapıldığında)**
function clearConfig() {
    resetConfig();
}

function getTenantId() {
    const config = readConfig();
    return config.tenant_id || null;
}

function getAccessToken() {
    const config = readConfig();
    return config.accessToken || null;
}

function getStoreName() {
    const config = readConfig();
    return config.storeName || null;
}

module.exports = {
    getTenantId,
    getAccessToken,
    getStoreName,
    setConfig, // Kullanıcı giriş yaptığında yeni bilgileri kaydedecek
    clearConfig // Kullanıcı çıkış yaptığında config'i sıfırlayacak
};
