// Gerekli modülleri içe aktarma
const { app, ipcMain, BrowserWindow, Tray, Menu, shell } = require("electron");
const os = require("os");
const path = require("path");
const ServerApp = require("../server/ServerApp").default;
const Options = require("../services/Options").default;
const printer = require("@thiagoelg/node-printer").default;
const SocketServer = require("../server/SocketServer").default;
const { AppConfig } = require("../server/config/app");
const originalFs = require("original-fs");
const Logger = require("../services/Logger").default;
const InternetChecker = require("../services/InternetChecker").default;
const AppMenu = require("./AppMenu").default;
const XMLParser = require("../server/services/XMLParser").default; // XMLParser'ı içe aktarma

// Uygulama dizinlerini ayarlama
process.env.DIST_ELECTRON = path.join(__dirname, "../");
process.env.DIST = path.join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL ? path.join(process.env.DIST_ELECTRON, "../public") : process.env.DIST;

// Protokol istemcisi ayarlama
if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient("nps", process.execPath, [path.resolve(process.argv[1])]);
  }
} else {
  app.setAsDefaultProtocolClient("nps");
}

// Donanım hızlandırmasını devre dışı bırakma
if (os.release().startsWith("6.1")) app.disableHardwareAcceleration();
if (process.platform === "win32") app.setAppUserModelId(app.getName());

// Tek örnek uygulama kilidi
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// Gerekli dizinleri oluşturma
const createDirectoryIfNotExists = (dirPath) => {
  if (!originalFs.existsSync(dirPath)) {
    originalFs.mkdirSync(dirPath);
  }
};

createDirectoryIfNotExists(AppConfig.paths.data);
createDirectoryIfNotExists(AppConfig.paths.storage);
createDirectoryIfNotExists(AppConfig.paths.build);

// Pencere ve diğer bileşenler için değişkenler
let win = null;
const preload = path.join(__dirname, "../preload/index.js");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = path.join(process.env.DIST, "index.html");
let server;
let socketServer;
let tray;

// Pencere oluşturma fonksiyonu
async function createWindow() {
  server = new ServerApp();
  socketServer = new SocketServer();
  
  // Sistem tepsisi için menü oluşturma
  tray = new Tray(path.join(process.env.PUBLIC, "favicon.ico"));
  const menuContext = Menu.buildFromTemplate([
    {
      label: "Restore Window",
      click: () => win.show()
    },
    {
      label: "Close And Quit",
      click: () => app.quit()
    }
  ]);
  
  tray.setToolTip("Nexo Print Server");
  tray.setContextMenu(menuContext);

  // Pencere oluşturma
  win = new BrowserWindow({
    title: "Nexo Print Server",
    icon: path.join(process.env.PUBLIC, "favicon.ico"),
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: true,
      devTools: !!process.env.VITE_DEV_SERVER_URL
    },
    maximizable: !!process.env.VITE_DEV_SERVER_URL
  });

  if (typeof process.env.VITE_DEV_SERVER_URL !== "string") {
    win.setSize(850, 600);
    win.setMaximumSize(850, 600);
    win.setMinimumSize(850, 600);
  }

  new AppMenu({ preload, win, indexHtml, url });
  
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(url);
    win.webContents.openDevTools();
  } else {
    win.loadFile(indexHtml);
  }

  // Pencere yükleme tamamlandığında
  win.webContents.on("did-finish-load", () => {
    win?.webContents.send("main-process-message", new Date().toLocaleString());
  });

  // Harici bağlantıları açma
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:")) shell.openExternal(url);
    return { action: "deny" };
  });

  // Pencereyi minimize etme
  win.on("minimize", (event) => {
    event.preventDefault();
    win.hide();
  });

  // İnternet bağlantı durumu kontrolü
  global.win = win;
  global.internetChecker = new InternetChecker();

  // İnternet bağlantısını kontrol etme fonksiyonu
  global.checkInternet = () => {
    const isConnected = new Options().get("internet_status");
    global.internetChecker.check().then((response) => {
      if (isConnected === "not_connected" && response) {
        new Options().set("internet_status", "connected");
        win?.webContents.send("refresh-options");
      }
      if (isConnected === "connected" && !response) {
        new Options().set("internet_status", "not_connected");
        win?.webContents.send("refresh-options");
      }
    }).catch(() => {
      if (isConnected === "connected") {
        new Options().set("internet_status", "not_connected");
        win?.webContents.send("refresh-options");
      }
    });
  };

  // Socket durumu güncellemeleri
  socketServer.state().subscribe((response) => {
    win?.webContents.send("socket-response", response);
  });

  // İnternet durumu kontrolü belirli aralıklarla
  setInterval(global.checkInternet, 5000);
}

// IPC (Inter-Process Communication) handle işlemleri
ipcMain.handle("save-options", (event, args) => {
  const response = new Options().saveFromObject(args, win);
  win.webContents.send("refresh-options");
  win.webContents.send("notification", {
    status: "success",
    message: "The settings were successfully saved."
  });
  return response;
});

ipcMain.handle("check-internet", () => {
  global.checkInternet();
});

ipcMain.handle("save-options-silently", (event, args) => {
  const response = new Options().saveFromObject(args, win);
  win.webContents.send("refresh-options");
  return response;
});

ipcMain.handle("delete-options", (event, args) => {
  const response = new Options().delete(args);
  win.webContents.send("refresh-options");
  return response;
});

ipcMain.handle("get-option", (event, args) => {
  return new Options().get(args);
});

ipcMain.handle("all-options", (event, args) => {
  return new Options().getAllOptions();
});

ipcMain.handle("system-details", (event, args) => {
  return {
    version: AppConfig.version,
    daily_print_limit: AppConfig.daily_print_limit
  };
});

ipcMain.handle("start-server", (event, args) => {
  return new Promise((resolve) => {
    try {
      const optionService = new Options();
      const options = optionService.getAllOptions();
      server.startServer(options);
      resolve({
        status: "success",
        message: "The server has successfully started."
      });
    } catch (exception) {
      resolve({
        status: "error",
        message: "An error occurred while starting the server.",
        exception
      });
    }
  });
});

// Diğer IPC handle işlemleri
ipcMain.handle("user-licenses", async (event, args) => {
  try {
    return result; // Burada 'result' tanımlı değil, gerçek kodda uygun değişkeni kullanmalısınız.
  } catch (exception) {
    throw exception.message;
  }
});

ipcMain.handle("api-hard-disconnect", async (event, args) => {
  try {
    return result; // Burada 'result' tanımlı değil, gerçek kodda uygun değişkeni kullanmalısınız.
  } catch (exception) {
    throw exception.message;
  }
});

ipcMain.handle("api-update-license", async (event, args) => {
  try {
    win.webContents.send("refresh-options");
    return result; // Burada 'result' tanımlı değil, gerçek kodda uygun değişkeni kullanmalısınız.
  } catch (exception) {
    throw exception;
  }
});

ipcMain.handle("load-daily-log", (event, args) => {
  return new Logger().getLogContent();
});

ipcMain.handle("clear-daily-log", (event, args) => {
  return new Logger().clear();
});

ipcMain.handle("stop-server", (event, args) => {
  server.stopServer();
});

ipcMain.handle("get-printers", (event, args) => {
  return printer.getPrinters();
});

ipcMain.handle("start-socket", (event, args) => {
  return new Promise((resolve, reject) => {
    try {
      socketServer.connect();
      resolve({
        status: "success",
        message: "Socket connection started"
      });
    } catch (exception) {
      reject(exception);
    }
  });
});

ipcMain.handle("stop-socket", (event, args) => {
  socketServer.disconnect();
});

// XML dosyasını yükleme fonksiyonu
ipcMain.handle("import-xml", async (event, filePath) => {
  try {
    const parsedData = await XMLParser.parseFile(filePath); // XML dosyasını parse etme
    return {
      status: "success",
      data: parsedData
    };
  } catch (error) {
    return {
      status: "error",
      message: error.message
    };
  }
});

// Uygulama başlatıldığında pencereyi oluşturma
app.whenReady().then(createWindow);

// Uygulama kapatıldığında tüm pencereleri kapatma
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// Uygulama yeniden etkinleştirildiğinde pencereyi oluşturma
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
