const { app, ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow, <PERSON>ray, <PERSON>u, shell } = require("electron");
const os = require("os");
const path = require("path");
const { 
    getTenantId, 
    setTenantId, 
    getAccessToken, 
    setAccessToken,
    getStoreName,
    setStoreName,
    clearConfig 
} = require('./config');
const { io } = require('socket.io-client');
const AutoLaunch = require('auto-launch');
const nodePrinter = require("@thiagoelg/node-printer");
const Logger = require("./services/Logger").default;
const { AppConfig } = require("./config/app");
const originalFs = require("original-fs");

// Donanım hızlandırmasını devre dışı bırakma
if (os.release().startsWith("6.1")) app.disableHardwareAcceleration();
if (process.platform === "win32") app.setAppUserModelId(app.getName());

// Tek örnek uygulama kilidi
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// Gerekli dizinleri oluşturma
const createDirectoryIfNotExists = (dirPath) => {
  if (!originalFs.existsSync(dirPath)) {
    originalFs.mkdirSync(dirPath);
  }
};

createDirectoryIfNotExists(AppConfig.paths.data);
createDirectoryIfNotExists(AppConfig.paths.storage);
createDirectoryIfNotExists(AppConfig.paths.build);

let autoLauncher = new AutoLaunch({
  name: 'SewPOS Printer',
  path: app.getPath('exe'),
});

let mainWindow;
let socket;
let tray = null; // Tray değişkenini global olarak tanımla


function createWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: false,
    icon: path.join(__dirname, 'assets' ,'icon.ico')
  });

  tray = new Tray(path.join(__dirname, 'assets' , 'icon.png')); // .png, .ico gibi bir ikon dosyanız olmalı
  const contextMenu = Menu.buildFromTemplate([
      { 
          label: 'Göster', 
          click: () => {
              mainWindow.show();
              mainWindow.focus();
          }
      },
      { type: 'separator' },
      { 
          label: 'Çıkış', 
          click: () => {
              app.quit();
          }
      }
  ]);

  tray.setToolTip('SewPOS Printer');
  tray.setContextMenu(contextMenu);

  // Tepsi ikonuna tıklandığında pencereyi göster
  tray.on('click', () => {
      mainWindow.show();
      mainWindow.focus();
  });

  // Pencere kapatıldığında tamamen kapatmak yerine tepsiye küçült
  mainWindow.on('close', (event) => {
      if (!app.isQuitting) {
          event.preventDefault();
          mainWindow.hide();
      }
      return false;
  });

  mainWindow.loadFile('index.html');
  setupSocketConnection();
}

async function checkAutoLaunchStatus() {
  try {
      const isEnabled = await autoLauncher.isEnabled();
      return isEnabled;
  } catch (err) {
      console.error('Auto launch check error:', err);
      return false;
  }
}

async function toggleAutoLaunch(enable) {
  try {
      if (enable) {
          await autoLauncher.enable();
      } else {
          await autoLauncher.disable();
      }
      return true;
  } catch (err) {
      console.error('Auto launch toggle error:', err);
      return false;
  }
}


function setupSocketConnection() {
  const tenantId = getTenantId();
  if (!tenantId) {
    console.log('Tenant ID bulunamadı');
    return;
  }

  socket = io('http://192.168.0.10:3000', {
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    reconnectionAttempts: Infinity
  });

  socket.on('connect', () => {
    console.log('Socket connected:', socket.id);
    socket.emit('authenticate', tenantId);
    mainWindow.webContents.send('socket-status', { connected: true });
  });

  socket.on('disconnect', () => {
    console.log('Socket disconnected');
    mainWindow.webContents.send('socket-status', { connected: false });
  });

  // Print event'ini main process'te dinleyip renderer'a iletiyoruz
  socket.on('print-order', (printData) => {
    mainWindow.webContents.send('print-order', printData);
  });
}

// IPC Handlers
ipcMain.handle('get-tenant-id', () => {
  return getTenantId();
});

ipcMain.handle('set-tenant-id', (event, tenant_id) => {
  setTenantId(tenant_id);
  setupSocketConnection();
});

ipcMain.handle("get-printers", (event, args) => {
  return nodePrinter.default.getPrinters();
});

ipcMain.handle('get-access-token', () => {
  return getAccessToken();
});

ipcMain.handle('set-access-token', (event, accessToken) => {
  setAccessToken(accessToken);
});

ipcMain.handle('get-app-path', (event) => {
  return app.getPath('appData');
});

ipcMain.handle('get-store-name', () => {
  return getStoreName();
});


ipcMain.handle('get-auto-launch-status', async () => {
  return await checkAutoLaunchStatus();
});

ipcMain.handle('toggle-auto-launch', async (event, enable) => {
  return await toggleAutoLaunch(enable);
});


ipcMain.handle('set-store-name', (event, storeName) => {
  setStoreName(storeName);
});

ipcMain.handle('clear-config', () => {
  clearConfig();
  if (socket) {
      socket.disconnect();
  }
});

ipcMain.handle('reconnect-socket', () => {
  setupSocketConnection();
});

app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
      if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
app.on('before-quit', () => {
  app.isQuitting = true;
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

ipcMain.handle("load-daily-log", (event, args) => {
  return new Logger().getLogContent();
});

ipcMain.handle("clear-daily-log", (event, args) => {
  return new Logger().clear();
});


ipcMain.handle("get-version", (event) => {
  return app.getVersion();
});


ipcMain.handle("system-details", (event, args) => {
  return {
    version: AppConfig.version,
    daily_print_limit: AppConfig.daily_print_limit
  };
});