const { app, BrowserWindow, Tray, <PERSON>u, shell, ipcMain } = require("electron");
const path = require("node:path");
const os = require("node:os");
const fs = require("original-fs");
const { setConfig, clearConfig, getTenantId, getAccessToken, getStoreName } = require('./config');
const { io } = require('socket.io-client');

const AutoLaunch = require('auto-launch');

let autoLauncher = new AutoLaunch({
  name: 'HollyPOS Printer',
  path: app.getPath('exe'),
});

let mainWindow;
let socket;
let tray = null; // Tray değişkenini global olarak tanımla


// **Tek uygulama örneği çalıştır**
if (!app.requestSingleInstanceLock()) {
  console.log('Uygulama zaten çalışıyor. İkinci örnek kapatılıyor...');
  app.quit();
  process.exit(0);
}

// İkinci bir örnek başlatıldığında, mevcut pencereyi göster
app.on('second-instance', (event, commandLine, workingDirectory) => {
  console.log('İkinci bir örnek başlatılmaya çalışıldı. Mevcut pencere gösteriliyor...');

  // Eğer pencere varsa, göster ve öne getir
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.show();
    mainWindow.focus();

    // Kullanıcıya bildirim göster
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('show-notification', {
        title: 'HollyPOS Printer',
        message: 'Uygulama zaten çalışıyor!'
      });
    }
  }
});



function createWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: false,
    icon: path.join(__dirname, 'assets' ,'icon.ico')
  });

  tray = new Tray(path.join(__dirname, 'assets' , 'icon.png')); // .png, .ico gibi bir ikon dosyanız olmalı
  const contextMenu = Menu.buildFromTemplate([
      {
          label: 'Göster',
          click: () => {
              mainWindow.show();
              mainWindow.focus();
          }
      },
      { type: 'separator' },
      {
          label: 'Çıkış',
          click: () => {
              app.quit();
          }
      }
  ]);

  tray.setToolTip('SewPOS Printer');
  tray.setContextMenu(contextMenu);

  // Tepsi ikonuna tıklandığında pencereyi göster
  tray.on('click', () => {
      mainWindow.show();
      mainWindow.focus();
  });

  // Pencere kapatıldığında tamamen kapatmak yerine tepsiye küçült
  mainWindow.on('close', (event) => {
      if (!app.isQuitting) {
          event.preventDefault();
          mainWindow.hide();
      }
      return false;
  });

  mainWindow.loadFile('index.html');
  setupSocketConnection();
}

async function checkAutoLaunchStatus() {
  try {
      const isEnabled = await autoLauncher.isEnabled();
      return isEnabled;
  } catch (err) {
      console.error('Auto launch check error:', err);
      return false;
  }
}

async function toggleAutoLaunch(enable) {
  try {
      if (enable) {
          await autoLauncher.enable();
      } else {
          await autoLauncher.disable();
      }
      return true;
  } catch (err) {
      console.error('Auto launch toggle error:', err);
      return false;
  }
}

function setupSocketConnection() {
  const tenantId = getTenantId();
  if (!tenantId) {
    console.log('Tenant ID bulunamadı');
    return;
  }

  // Eğer zaten bir socket bağlantısı varsa, önce onu kapat
  if (socket) {
    console.log('Mevcut socket bağlantısı kapatılıyor...');
    socket.removeAllListeners(); // Tüm dinleyicileri temizle
    socket.disconnect();
    socket = null;
  }

  console.log('Yeni socket bağlantısı oluşturuluyor...');
  socket = io('https://hollypos-backend.uygulama.sewpos.com/', {
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    reconnectionAttempts: Infinity
  });

  socket.on('connect', () => {
    console.log('Socket connected:', socket.id);
    socket.emit('authenticate', tenantId);
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('socket-status', { connected: true });
    }
  });

  socket.on('disconnect', () => {
    console.log('Socket disconnected');
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('socket-status', { connected: false });
    }
  });

  // Print event'ini main process'te dinleyip renderer'a iletiyoruz
  socket.on('print-order', (printData) => {
    console.log('Yeni yazdırma işi alındı:', printData.length);
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('print-order', printData);
    }
  });
}


ipcMain.handle('set-tenant-id', (event, tenant_id, accessToken, storeName) => {
  setConfig(tenant_id, accessToken, storeName);
  setupSocketConnection();
});

ipcMain.handle('get-access-token', () => {
  return getAccessToken();
});

ipcMain.handle('get-store-name', () => {
  return getStoreName();
});

ipcMain.handle('get-tenant-id', () => {
  return getTenantId();
});

ipcMain.handle('get-app-path', () => {
  return app.getPath('appData');
});


ipcMain.handle('get-auto-launch-status', async () => {
  return await checkAutoLaunchStatus();
});

ipcMain.handle('toggle-auto-launch', async (event, enable) => {
  return await toggleAutoLaunch(enable);
});


ipcMain.handle('set-store-name', (event, storeName) => {
  setStoreName(storeName);
});

ipcMain.handle('clear-config', () => {
  clearConfig();
  if (socket) {
      socket.disconnect();
  }
});

ipcMain.handle('reconnect-socket', () => {
  setupSocketConnection();
});

// Uygulamayı belirli bir saatte otomatik yeniden başlatma
function scheduleAppRestart() {
  // Her gece saat 3'te uygulamayı yeniden başlat
  const now = new Date();
  const night = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate() + 1, // yarın
    3, 0, 0 // saat 03:00:00
  );
  const timeToRestart = night.getTime() - now.getTime();

  console.log(`Uygulama ${new Date(night).toLocaleString()} tarihinde otomatik olarak yeniden başlatılacak.`);

  setTimeout(() => {
    console.log('Uygulama otomatik olarak yeniden başlatılıyor...');
    app.relaunch();
    app.exit();
  }, timeToRestart);
}

app.whenReady().then(() => {
  createWindow();

  // Otomatik yeniden başlatmayı planla
  scheduleAppRestart();

  app.on('activate', function () {
      if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
app.on('before-quit', () => {
  app.isQuitting = true;
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});
