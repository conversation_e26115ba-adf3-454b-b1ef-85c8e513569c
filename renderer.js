const { ipc<PERSON>enderer } = require('electron');
const axios = require('axios');
const { XMLParser } = require('./printer');
const { ThermalPrinter, PrinterTypes } = require('node-thermal-printer');
const electronPrinter = require('@thiagoelg/node-printer');


let tenantId = null;
let accessToken = null;
const API_BASE_URL = 'http://************:3000/api/v1';

document.addEventListener('DOMContentLoaded', async () => {
    tenantId = await ipcRenderer.invoke('get-tenant-id');
    accessToken = await ipcRenderer.invoke('get-access-token');
    updateNetworkStatus();

    window.addEventListener('online', () => {
        updateNetworkStatus();
        if (navigator.onLine) {
            ipcRenderer.invoke('reconnect-socket');
            checkPendingPrints();
        }
    });
    window.addEventListener('offline', updateNetworkStatus);

    if (tenantId && accessToken) {
        showPrinterControl();
        checkPrinterStatus();
        await updateUserInfo();
    }
    document.getElementById('logoutButton').addEventListener('click', logout);

    const autoLaunchToggle = document.getElementById('autoLaunchToggle');
    const isAutoLaunchEnabled = await ipcRenderer.invoke('get-auto-launch-status');
    autoLaunchToggle.checked = isAutoLaunchEnabled;

    // Switch değiştiğinde auto launch'ı güncelle
    autoLaunchToggle.addEventListener('change', async (e) => {
        const success = await ipcRenderer.invoke('toggle-auto-launch', e.target.checked);
        if (!success) {
            // Başarısız olursa switch'i eski haline getir
            e.target.checked = !e.target.checked;
            // Hata mesajı göster
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = 'Otomatik başlatma ayarı değiştirilemedi';
            statusDiv.style.color = 'red';
        }
    });
    document.getElementById('checkPrinterButton').addEventListener('click', () => {
        checkPrinterStatus();
    });

});

ipcMain.handle("get-version", (event) => {
    return app.getVersion();
  });

async function fetchAppVersion() {
    const version = await ipcRenderer.invoke('get-version');
    console.log("App Version:", version);
}

fetchAppVersion();


// Socket durumu dinleyicisi
ipcRenderer.on('socket-status', (event, data) => {
    const socketStatusDiv = document.getElementById('socketStatus');
    if (data.connected) {
        socketStatusDiv.textContent = 'Socket Bağlantısı: Bağlı';
        socketStatusDiv.style.color = 'green';
    } else {
        socketStatusDiv.textContent = 'Socket Bağlantısı: Bağlı değil';
        socketStatusDiv.style.color = 'red';
    }
});



// Print order dinleyicisi
ipcRenderer.on('print-order', async (event, printData) => {
    try {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = 'Yeni sipariş yazdırılıyor...';
        statusDiv.style.color = 'blue';

        if (printData && printData.length > 0) {
            for (const printItem of printData) {
                if (printItem.content && printItem.status === 2) {
                    const xmlParser = new XMLParser();
                    await xmlParser.print(printItem.content);
                    
                    // Başarılı yazdırma durumunu backend'e bildir
                    try {
                        await axios.put(`${API_BASE_URL}/print/update-status/${printItem.id}`, {
                            status: '1'
                        }, {
                            headers: { 'Authorization': `Bearer ${accessToken}` }
                        });
                    } catch (error) {
                        console.error('Status update error:', error);
                    }
                }
            }
            statusDiv.textContent = 'Yazdırma başarılı!';
            statusDiv.style.color = 'green';
        }
    } catch (error) {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = `Yazdırma hatası: ${error.message}`;
        statusDiv.style.color = 'red';
        console.error('Print error:', error);
    }
});

async function checkPendingPrints() {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = 'Bekleyen siparişler kontrol ediliyor...';
    statusDiv.style.color = 'blue';

    try {
        const response = await axios.get(`${API_BASE_URL}/print/get`, {
            headers: { 'Authorization': `Bearer ${accessToken}` },
            params: { tenant_id: tenantId }
        });

        if (response.data.success && response.data.printData && response.data.printData.length > 0) {
            for (const printItem of response.data.printData) {
                if (printItem.content && printItem.status === 2) {
                    const xmlParser = new XMLParser();
                    await xmlParser.print(printItem.content);

                    // Update print status
                    try {
                        await axios.put(`${API_BASE_URL}/print/update-status/${printItem.id}`, {
                            status: '1'
                        }, {
                            headers: { 'Authorization': `Bearer ${accessToken}` }
                        });
                    } catch (error) {
                        console.error('Status update error:', error);
                    }
                }
            }
            statusDiv.textContent = 'Bekleyen siparişler yazdırıldı!';
            statusDiv.style.color = 'green';
        } else {
            statusDiv.textContent = 'Bekleyen sipariş bulunamadı.';
            statusDiv.style.color = 'blue';
        }
    } catch (error) {
        console.error('Error fetching pending prints:', error);
        statusDiv.textContent = `Hata: ${error.message}`;
        statusDiv.style.color = 'red';
    }
}


function checkPrinterStatus() {
    const printerStatusDiv = document.getElementById('printerStatus');
    const checkPrinterButton = document.getElementById('checkPrinterButton');
    
    // Butonu devre dışı bırak
    checkPrinterButton.disabled = true;
    
    
    const printer = new ThermalPrinter({
        type: PrinterTypes.EPSON,
        interface: '//localhost/printer',
        characterSet: 'SLOVENIA'
    });

    printer.isPrinterConnected().then(isConnected => {
        if (isConnected) {
            printerStatusDiv.textContent = 'Yazıcı Durumu: Bağlandı';
            printerStatusDiv.style.color = 'green';
        } else {
            printerStatusDiv.textContent = 'Yazıcı Durumu: Bağlantı yok';
            printerStatusDiv.style.color = 'red';
        }
    }).catch(err => {
        console.error('Yazıcı kontrol hatası:', err);
        console.log('Hata detayları:', {
            message: err.message,
            code: err.code,
            interface: printer.interface
        });
        printerStatusDiv.textContent = 'Yazıcı Durumu: Hata';
        printerStatusDiv.style.color = 'red';
    }).finally(() => {
        // Butonu tekrar aktif et
        checkPrinterButton.disabled = false;
    });
}

function updateNetworkStatus() {
    const networkStatusDiv = document.getElementById('networkStatus');
    if (navigator.onLine) {
        networkStatusDiv.textContent = 'İnternet: Bağlı';
        networkStatusDiv.style.color = 'green';
    } else {
        networkStatusDiv.textContent = 'İnternet: Bağlantı yok';
        networkStatusDiv.style.color = 'red';
    }
}

async function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const statusDiv = document.getElementById('status');

    try {
        const response = await axios.post(`${API_BASE_URL}/auth/signin`, { username, password });
        console.log("Login response:", response.data);
        if (response.data.success) {
            tenantId = response.data.user.tenant_id;
            accessToken = response.data.accessToken;
            const storeName = response.data.user.store_name;
            await ipcRenderer.invoke('set-tenant-id', tenantId);
            await ipcRenderer.invoke('set-access-token', accessToken);
            await ipcRenderer.invoke('set-store-name', storeName); // Store adını kaydet

            
            // Restoran adını kaydet
            if (response.data?.user?.store_name) {
                await ipcRenderer.invoke('set-store-name', response.data.user.store_name);
            }
            
            showPrinterControl();
            updateUserInfo();
            checkPendingPrints();
            
            statusDiv.textContent = 'Giriş başarılı!';
            statusDiv.style.color = 'green';
        } else {
            statusDiv.textContent = 'Giriş başarısız. Lütfen tekrar deneyin.';
            statusDiv.style.color = 'red';
        }
    } catch (error) {
        statusDiv.textContent = `Giriş hatası: ${error.message}`;
        statusDiv.style.color = 'red';
    }
}

async function updateUserInfo() {
    const storeNameSpan = document.getElementById('storeName');
    const tenantIdSpan = document.getElementById('tenantId');
    
    const storeName = await ipcRenderer.invoke('get-store-name');
    const tenantId = await ipcRenderer.invoke('get-tenant-id');
    
    storeNameSpan.textContent = storeName || 'Bilinmeyen Restoran';
    tenantIdSpan.textContent = `Tenant ID: ${tenantId}`;
}

async function logout() {
    try {
        // Config'i temizle
        await ipcRenderer.invoke('clear-config');
        
        // Değişkenleri sıfırla
        tenantId = null;
        accessToken = null;
        
        // Login formunu göster
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('printerControl').style.display = 'none';
        
        // Status mesajını güncelle
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = 'Çıkış yapıldı';
        statusDiv.style.color = 'blue';
        
        // Sayfayı yenile
        window.location.reload();
    } catch (error) {
        console.error('Çıkış hatası:', error);
        toast.error('Çıkış yapılırken bir hata oluştu');
    }
}

function showPrinterControl() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('printerControl').style.display = 'block';
}

document.getElementById('loginButton').addEventListener('click', login);
document.getElementById('printButton').addEventListener('click', checkPendingPrints);