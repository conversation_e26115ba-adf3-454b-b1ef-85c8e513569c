const { ipcRenderer } = require('electron');
const axios = require('axios');
const XMLParserTwo = require('./XMLParserTwo'); // Doğru import
const { ThermalPrinter, PrinterTypes } = require('node-thermal-printer');
const PrinterDriver = require("@thiagoelg/node-printer");


let tenantId = null;
let accessToken = null;
const API_BASE_URL = 'https://backendpos.hollystone.com.tr/api/v1';

// Bildirim gösterme fonksiyonu
function showNotification(title, message) {
    const statusDiv = document.getElementById('status');
    if (statusDiv) {
        statusDiv.textContent = message;
        statusDiv.style.color = 'blue';
    }

    // Tarayıcı bildirimi göster (izin varsa)
    if (Notification.permission === 'granted') {
        new Notification(title, { body: message });
    } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                new Notification(title, { body: message });
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', async () => {
    tenantId = await ipcRenderer.invoke('get-tenant-id');
    accessToken = await ipcRenderer.invoke('get-access-token');
    updateNetworkStatus();

    window.addEventListener('online', () => {
        updateNetworkStatus();
        if (navigator.onLine) {
            ipcRenderer.invoke('reconnect-socket');
            checkPendingPrints();
        }
    });
    window.addEventListener('offline', updateNetworkStatus);

    if (tenantId && accessToken) {
        showPrinterControl();
        checkPrinterStatus();
        await updateUserInfo();
    }
    document.getElementById('logoutButton').addEventListener('click', logout);

    const autoLaunchToggle = document.getElementById('autoLaunchToggle');
    const isAutoLaunchEnabled = await ipcRenderer.invoke('get-auto-launch-status');
    autoLaunchToggle.checked = isAutoLaunchEnabled;

    // Switch değiştiğinde auto launch'ı güncelle
    autoLaunchToggle.addEventListener('change', async (e) => {
        const success = await ipcRenderer.invoke('toggle-auto-launch', e.target.checked);
        if (!success) {
            // Başarısız olursa switch'i eski haline getir
            e.target.checked = !e.target.checked;
            // Hata mesajı göster
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = 'Otomatik başlatma ayarı değiştirilemedi';
            statusDiv.style.color = 'red';
        }
    });

    // Bildirim izni iste
    if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
        Notification.requestPermission();
    }
});

// Bildirim dinleyicisi
ipcRenderer.on('show-notification', (event, data) => {
    showNotification(data.title, data.message);
});

// Socket durumu dinleyicisi
ipcRenderer.on('socket-status', (event, data) => {
    const socketStatusDiv = document.getElementById('socketStatus');
    if (data.connected) {
        socketStatusDiv.textContent = 'Socket Bağlantısı: Bağlı';
        socketStatusDiv.style.color = 'green';
    } else {
        socketStatusDiv.textContent = 'Socket Bağlantısı: Bağlı değil';
        socketStatusDiv.style.color = 'red';
    }
});

// Yazdırılan işleri takip etmek için global değişken
let printedJobIds = new Set();

// Print order dinleyicisini temizle ve yeniden ekle
ipcRenderer.removeAllListeners('print-order');

// Print order dinleyicisi
ipcRenderer.on('print-order', async (event, printData) => {
    try {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = 'Yeni sipariş yazdırılıyor...';
        statusDiv.style.color = 'blue';
        console.log(`${new Date().toISOString()} - Yazdırma işi alındı, toplam: ${printData?.length || 0}`);

        if (printData && printData.length > 0) {
            for (const printItem of printData) {
                // Daha önce yazdırılmış mı kontrol et
                if (printItem.content && printItem.status === 2 && !printedJobIds.has(printItem.id)) {
                    console.log(`${new Date().toISOString()} - Yazdırılıyor: ${printItem.id}`);

                    // Önce yazdırılacak olarak işaretle
                    printedJobIds.add(printItem.id);

                    // Yazdırma işlemini gerçekleştir
                    const xmlParserInstance = new XMLParserTwo();
                    await xmlParserInstance.print(printItem.content);

                    // Başarılı yazdırma durumunu backend'e bildir
                    try {
                        await axios.put(`${API_BASE_URL}/print/update-status/${printItem.id}`, {
                            status: '1'
                        }, {
                            headers: { 'Authorization': `Bearer ${accessToken}` }
                        });
                        console.log(`${new Date().toISOString()} - Durum güncellendi: ${printItem.id}`);

                        // Belirli bir süre sonra ID'yi temizle (30 dakika)
                        setTimeout(() => {
                            printedJobIds.delete(printItem.id);
                            console.log(`${new Date().toISOString()} - ID temizlendi: ${printItem.id}`);
                        }, 30 * 60 * 1000);
                    } catch (error) {
                        console.error('Status update error:', error);
                    }
                } else if (printedJobIds.has(printItem.id)) {
                    console.log(`${new Date().toISOString()} - Zaten yazdırılmış, atlanıyor: ${printItem.id}`);
                }
            }
            statusDiv.textContent = 'Yazdırma başarılı!';
            statusDiv.style.color = 'green';
        }
    } catch (error) {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = `Yazdırma hatası: ${error.message}`;
        statusDiv.style.color = 'red';
        console.error('Print error:', error);
    }
});

async function checkPendingPrints() {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = 'Bekleyen siparişler kontrol ediliyor...';
    statusDiv.style.color = 'blue';
    console.log(`${new Date().toISOString()} - Bekleyen siparişler kontrol ediliyor...`);

    try {
        const response = await axios.get(`${API_BASE_URL}/print/get`, {
            headers: { 'Authorization': `Bearer ${accessToken}` },
            params: { tenant_id: tenantId }
        });

        if (response.data.success && response.data.printData && response.data.printData.length > 0) {
            console.log(`${new Date().toISOString()} - ${response.data.printData.length} bekleyen sipariş bulundu`);

            for (const printItem of response.data.printData) {
                // Daha önce yazdırılmış mı kontrol et
                if (printItem.content && printItem.status === 2 && !printedJobIds.has(printItem.id)) {
                    console.log(`${new Date().toISOString()} - Manuel kontrol: Yazdırılıyor: ${printItem.id}`);

                    // Önce yazdırılacak olarak işaretle
                    printedJobIds.add(printItem.id);

                    // Yazdırma işlemini gerçekleştir
                    const xmlParserInstance = new XMLParserTwo();
                    await xmlParserInstance.print(printItem.content);

                    // Update print status
                    try {
                        await axios.put(`${API_BASE_URL}/print/update-status/${printItem.id}`, {
                            status: '1'
                        }, {
                            headers: { 'Authorization': `Bearer ${accessToken}` }
                        });
                        console.log(`${new Date().toISOString()} - Manuel kontrol: Durum güncellendi: ${printItem.id}`);

                        // Belirli bir süre sonra ID'yi temizle (30 dakika)
                        setTimeout(() => {
                            printedJobIds.delete(printItem.id);
                            console.log(`${new Date().toISOString()} - ID temizlendi: ${printItem.id}`);
                        }, 30 * 60 * 1000);
                    } catch (error) {
                        console.error('Status update error:', error);
                    }
                } else if (printedJobIds.has(printItem.id)) {
                    console.log(`${new Date().toISOString()} - Manuel kontrol: Zaten yazdırılmış, atlanıyor: ${printItem.id}`);
                }
            }
            statusDiv.textContent = 'Bekleyen siparişler yazdırıldı!';
            statusDiv.style.color = 'green';
        } else {
            console.log(`${new Date().toISOString()} - Bekleyen sipariş bulunamadı`);
            statusDiv.textContent = 'Bekleyen sipariş bulunamadı.';
            statusDiv.style.color = 'blue';
        }
    } catch (error) {
        console.error('Error fetching pending prints:', error);
        statusDiv.textContent = `Hata: ${error.message}`;
        statusDiv.style.color = 'red';
    }
}

let checkingPrinter = false; // Tekrar eden kontrolü önlemek için


function checkPrinterStatus() {
    if (checkingPrinter) return; // Zaten çalışıyorsa tekrar başlatma
    checkingPrinter = true;

    const printerStatusDiv = document.getElementById('printerStatus');

    const printer = new ThermalPrinter({
        type: PrinterTypes.EPSON,
        interface: 'printer:POS Printer 203DPI  Series',
        characterSet: 'SLOVENIA',
        driver: PrinterDriver,
    });

    printer.isPrinterConnected().then(isConnected => {
        if (isConnected) {
            printerStatusDiv.textContent = 'Yazıcı Durumu: Bağlandı';
            printerStatusDiv.style.color = 'green';
        } else {
            printerStatusDiv.textContent = 'Yazıcı Durumu: Bağlantı yok';
            printerStatusDiv.style.color = 'red';
        }
        checkingPrinter = false; // Kontrol tamamlandı, tekrar çalıştırılabilir
    }).catch(err => {
        printerStatusDiv.textContent = 'Yazıcı Durumu: Hata';
        printerStatusDiv.style.color = 'red';
        console.error('Yazıcı kontrol hatası:', err);
        checkingPrinter = false;
    });
}

function updateNetworkStatus() {
    const networkStatusDiv = document.getElementById('networkStatus');
    if (navigator.onLine) {
        networkStatusDiv.textContent = 'İnternet: Bağlı';
        networkStatusDiv.style.color = 'green';
    } else {
        networkStatusDiv.textContent = 'İnternet: Bağlantı yok';
        networkStatusDiv.style.color = 'red';
    }
}

async function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const statusDiv = document.getElementById('status');

    try {
        console.log("Login başladı...");
        const response = await axios.post(`${API_BASE_URL}/auth/signin`, { username, password });
        console.log("Login response:", response.data);

        if (response.data.success) {
            const tenantId = response.data.user.tenant_id;
            const accessToken = response.data.accessToken;
            const storeName = response.data.user.store_name;

            console.log("Tenant ID:", tenantId);
            console.log("Access Token:", accessToken);
            console.log("Store Name:", storeName);

            // **Tüm verileri tek seferde kaydet ve socket bağlantısını başlat**
            await ipcRenderer.invoke('set-tenant-id', tenantId, accessToken, storeName);
            console.log("Config kaydedildi ve socket bağlantısı başlatıldı.");

            showPrinterControl(); // **Bu çalışıyor mu? Kontrol edelim**
            console.log("showPrinterControl çağrıldı.");

            await updateUserInfo(); // Kullanıcı bilgilerini güncelle
            console.log("updateUserInfo çağrıldı.");

            checkPendingPrints(); // Bekleyen çıktıları kontrol et
            console.log("checkPendingPrints çağrıldı.");

            statusDiv.textContent = 'Giriş başarılı!';
            statusDiv.style.color = 'green';

        } else {
            statusDiv.textContent = 'Giriş başarısız. Lütfen tekrar deneyin.';
            statusDiv.style.color = 'red';
        }
    } catch (error) {
        console.error("Giriş hatası:", error);
        statusDiv.textContent = `Giriş hatası: ${error.message}`;
        statusDiv.style.color = 'red';
    }
}


async function updateUserInfo() {
    const storeNameSpan = document.getElementById('storeName');
    const tenantIdSpan = document.getElementById('tenantId');

    const storeName = await ipcRenderer.invoke('get-store-name');
    const tenantId = await ipcRenderer.invoke('get-tenant-id');

    storeNameSpan.textContent = storeName || 'Bilinmeyen Restoran';
    tenantIdSpan.textContent = `Tenant ID: ${tenantId}`;
}

async function logout() {
    try {
        // Config'i temizle
        await ipcRenderer.invoke('clear-config');

        // Değişkenleri sıfırla
        tenantId = null;
        accessToken = null;

        // Login formunu göster
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('printerControl').style.display = 'none';

        // Status mesajını güncelle
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = 'Çıkış yapıldı';
        statusDiv.style.color = 'blue';

        // Sayfayı yenile
        window.location.reload();
    } catch (error) {
        console.error('Çıkış hatası:', error);
        toast.error('Çıkış yapılırken bir hata oluştu');
    }
}

function showPrinterControl() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('printerControl').style.display = 'block';
}

document.getElementById('loginButton').addEventListener('click', login);
document.getElementById('printButton').addEventListener('click', checkPendingPrints);