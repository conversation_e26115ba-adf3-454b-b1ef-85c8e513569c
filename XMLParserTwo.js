const ThermalPrinter = require("node-thermal-printer");
const { XMLParser } = require('fast-xml-parser');
const fs = require("fs");
const { DOMParser } = require("xmldom");
const PrinterDriver = require("@thiagoelg/node-printer");
const Logger = require("./Logger");

class XMLParserTwo {
  constructor(logger = new Logger()) {
    this.logger = logger;
    this.config = { interface: "" };
    this.fileCreated = [];
  }

  getTagValue(tagName, parent) {
    const result = parent.filter((tag) => Object.keys(tag)[0] === tagName);
    return result.length === 1 ? result[0][tagName][0]["#text"] || false : false;
  }

  async print(xml) {
    this.fileCreated = [];

    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@",
      attributesGroupName: "attributes",
      preserveOrder: true
    });
    

    let result;
    try {
      result = parser.parse(xml);
    } catch (error) {
      this.logger.log("Error parsing XML document", error);
      return { status: "failed", message: "Invalid XML format" };
    }

    const document = result.filter((tag) => tag.document !== undefined);
    if (!result[1]?.configuration) {
      return { status: "failed", message: "Invalid Print Job Document" };
    }

    this.config = {
      characterSet: this.getTagValue("characterset", result[1].configuration) || "",
      interface: this.getTagValue("interface", result[1].configuration),
      lineCharacter: this.getTagValue("line-character", result[1].configuration),
      type: this.getTagValue("type", result[1].configuration) || ThermalPrinter.types.EPSON,
      driver: PrinterDriver,
      removeSpecialCharacters: !!result[1].configuration[0]?.["remove-special-chars"]
    };

    this.thermalPrinter = new ThermalPrinter.printer(this.config);

    if (document.length > 0) {
      try {
        await this.walker(document[0].document);
      } catch (error) {
        this.logger.log("Error processing XML document", error);
      }
    }

    // Dosyaları temizle
    this.fileCreated.forEach((file) => {
      if (fs.existsSync(file)) fs.unlinkSync(file);
    });

    try {
      await this.thermalPrinter.execute();
      return { status: "success", message: "Printed" };
    } catch (error) {
      this.logger.log("Print execution error", error);
      return { status: "failed", message: error.toString() };
    }
  }

  injectAds(xmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlString, "text/xml");
    const targetNode =
      doc.getElementsByTagName("paper-cut")[0] ||
      doc.getElementsByTagName("full-cut")[0] ||
      doc.getElementsByTagName("partial-cut")[0] ||
      doc.getElementsByTagName("document")[0];

    const newElement = doc.createElement("align");
    newElement.setAttribute("mode", "center");

    const text1 = doc.createElement("text-line");
    text1.textContent = "SewPos Retoran Yönetim Yazılımları";
    newElement.appendChild(text1);

    targetNode.parentNode.insertBefore(newElement, targetNode);
    return doc.toString();
  }

  async walker(result) {
    for (const tag of result) {
      const tagName = Object.keys(tag)[0];

      switch (tagName) {
        case "align":
          if (tag.align[0]["@mode"] === "center") {
            this.thermalPrinter.alignCenter();
          } else if (tag.align[0]["@mode"] === "right") {
            this.thermalPrinter.alignRight();
          } else {
            this.thermalPrinter.alignLeft();
          }
          await this.walker(tag.align);
          this.thermalPrinter.alignLeft(); // Geri sola döndür
          break;
        case "double-height":
          this.thermalPrinter.setTextDoubleHeight();
          await this.walker(tag[tagName]);
          this.thermalPrinter.setTextNormal();
          break;
        case "double-width":
          this.thermalPrinter.setTextDoubleWidth();
          await this.walker(tag[tagName]);
          this.thermalPrinter.setTextNormal();
          break;
        case "text-line":
          if (tag[tagName][0]) {
            this.thermalPrinter.println(tag[tagName][0]["#text"] || "");
          }
          break;
        case "bold":
          this.thermalPrinter.bold(true);
          await this.walker(tag[tagName]);
          this.thermalPrinter.bold(false);
          break;
        case "text":
          await this.walker(tag[tagName]);
          break;
        case "line-separator":
          this.thermalPrinter.drawLine();
          break;
        case "line-feed":
          this.thermalPrinter.newLine();
          break;
        case "full-cut":
          this.thermalPrinter.cut();
          break;
        case "partial-cut":
        case "paper-cut":
          this.thermalPrinter.partialCut();
          break;
        case "qr-code":
          this.thermalPrinter.printQR(tag[tagName][0]["#text"] || "", { cellSize: 8 });
          break;
        case "beep":
          this.thermalPrinter.beep();
          break;
      }
    }
}



  setDirection(direction) {
    switch (direction) {
      case "center":
        this.thermalPrinter.alignCenter();
        break;
      case "left":
        this.thermalPrinter.alignLeft();
        break;
      case "right":
        this.thermalPrinter.alignRight();
        break;
    }
  }

  resetDirection() {
    this.thermalPrinter.alignLeft();
  }

  generateRandomString(length) {
    return Array.from({ length }, () =>
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(Math.random() * 62))
    ).join("");
  }
}

module.exports = XMLParserTwo;
