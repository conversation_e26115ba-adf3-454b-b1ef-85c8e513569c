const ThermalPrinter = require("node-thermal-printer");
const { XMLParser } = require('fast-xml-parser');
const fs = require("fs");
const path = require("path");
const { DOMParser } = require("xmldom");
const PrinterDriver = require("@thiagoelg/node-printer");
const Logger = require("./Logger");
const axios = require("axios");
const jimp = require("jimp");
const puppeteer = require("puppeteer");

class XMLParserTwo {
  constructor(logger = new Logger()) {
    this.logger = logger;
    this.config = { interface: "" };
    this.fileCreated = [];

    // Storage dizinini oluştur
    this.storagePath = path.join(process.env.APPDATA || process.env.HOME, 'sewpos', 'storage');
    this.ensureStorageDirectory();
  }

  ensureStorageDirectory() {
    try {
      if (!fs.existsSync(this.storagePath)) {
        fs.mkdirSync(this.storagePath, { recursive: true });
        this.logger.log(`Storage dizini oluşturuldu: ${this.storagePath}`);
      }
    } catch (error) {
      this.logger.log(`Storage dizini oluşturma hatası: ${error.message}`);
    }
  }

  getTagValue(tagName, parent) {
    const result = parent.filter((tag) => Object.keys(tag)[0] === tagName);
    return result.length === 1 ? result[0][tagName][0]["#text"] || false : false;
  }

  async print(xml) {
    this.fileCreated = [];

    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@",
      attributesGroupName: "attributes",
      preserveOrder: true
    });


    let result;
    try {
      result = parser.parse(xml);
    } catch (error) {
      this.logger.log("Error parsing XML document", error);
      return { status: "failed", message: "Invalid XML format" };
    }

    const document = result.filter((tag) => tag.document !== undefined);
    if (!result[1]?.configuration) {
      return { status: "failed", message: "Invalid Print Job Document" };
    }

    this.config = {
      characterSet: this.getTagValue("characterset", result[1].configuration) || "",
      interface: this.getTagValue("interface", result[1].configuration),
      lineCharacter: this.getTagValue("line-character", result[1].configuration),
      type: this.getTagValue("type", result[1].configuration) || ThermalPrinter.types.EPSON,
      driver: PrinterDriver,
      removeSpecialCharacters: !!result[1].configuration[0]?.["remove-special-chars"]
    };

    this.thermalPrinter = new ThermalPrinter.printer(this.config);

    if (document.length > 0) {
      try {
        await this.walker(document[0].document);
      } catch (error) {
        this.logger.log("Error processing XML document", error);
      }
    }

    // Dosyaları temizle
    await this.cleanupFiles();

    try {
      await this.thermalPrinter.execute();
      return { status: "success", message: "Printed" };
    } catch (error) {
      this.logger.log("Print execution error", error);
      return { status: "failed", message: error.toString() };
    }
  }

  injectAds(xmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlString, "text/xml");
    const targetNode =
      doc.getElementsByTagName("paper-cut")[0] ||
      doc.getElementsByTagName("full-cut")[0] ||
      doc.getElementsByTagName("partial-cut")[0] ||
      doc.getElementsByTagName("document")[0];

    const newElement = doc.createElement("align");
    newElement.setAttribute("mode", "center");

    const text1 = doc.createElement("text-line");
    text1.textContent = "SewPos Retoran Yönetim Yazılımları";
    newElement.appendChild(text1);

    targetNode.parentNode.insertBefore(newElement, targetNode);
    return doc.toString();
  }

  async walker(result) {
    for (const tag of result) {
      const tagName = Object.keys(tag)[0];
      const attrParentName = tag.attributes ? "attributes" : tagName;

      try {
        switch (tagName) {
          case "align":
            if (tag.align[0]["@mode"] === "center") {
              this.thermalPrinter.alignCenter();
              } else if (tag.align[0]["@mode"] === "right") {
              this.thermalPrinter.alignRight();
            } else {
              this.thermalPrinter.alignLeft();
            }
            await this.walker(tag.align);
            this.thermalPrinter.alignLeft(); // Geri sola döndür
            break;

          case "font":
            const fontType = tag[attrParentName]?.["@type"] || "a";
            this.setFontType(fontType);
            await this.walker(tag[tagName]);
            this.setFontType("a"); // Varsayılan font'a dön
            break;

          case "double-height":
            this.thermalPrinter.setTextDoubleHeight();
            await this.walker(tag[tagName]);
            this.thermalPrinter.setTextNormal();
            break;

          case "double-width":
            this.thermalPrinter.setTextDoubleWidth();
            await this.walker(tag[tagName]);
            this.thermalPrinter.setTextNormal();
            break;

          case "quad-size":
            this.thermalPrinter.setTextQuadArea();
            await this.walker(tag[tagName]);
            this.thermalPrinter.setTextNormal();
            break;

          case "text-line":
            if (tag[tagName][0]) {
              this.thermalPrinter.println(tag[tagName][0]["#text"] || "");
            }
            break;

          case "space-between":
            if (tag[tagName][0]?.text && tag[tagName][1]?.text) {
              const leftText = tag[tagName][0].text[0]["#text"] || "";
              const rightText = tag[tagName][1].text[0]["#text"] || "";
              this.thermalPrinter.leftRight(leftText, rightText);
            }
            break;

          case "table-row":
            await this.renderTable(tag[tagName]);
            break;

          case "bold":
            this.thermalPrinter.bold(true);
            if (typeof tag[tagName][0]["#text"] === "string") {
              this.thermalPrinter.println(tag[tagName][0]["#text"]);
            } else {
              await this.walker(tag[tagName]);
            }
            this.thermalPrinter.bold(false);
            break;

          case "invert":
            this.thermalPrinter.invert(true);
            if (typeof tag[tagName][0]["#text"] === "string") {
              this.thermalPrinter.println(tag[tagName][0]["#text"]);
            } else {
              await this.walker(tag[tagName]);
            }
            this.thermalPrinter.invert(false);
            break;

          case "text":
            this.thermalPrinter.setTextNormal();
            await this.walker(tag[tagName]);
            this.thermalPrinter.setTextNormal();
            break;

          case "image":
            if (tag[tagName][0] && tag[tagName][0]["#text"]) {
              await this.downloadImage(tag[tagName][0]["#text"]);
            }
            break;

          case "web-url":
            if (tag[tagName][0] && tag[tagName][0]["#text"]) {
              await this.capture(tag[tagName][0]["#text"]);
            }
            break;

          case "base64":
            if (tag[tagName][0] && tag[tagName][0]["#text"]) {
              const base64Data = tag[tagName][0]["#text"];
              const dataBuffer = Buffer.from(base64Data, "base64");
              const filePath = path.join(this.storagePath, `base64-${this.generateRandomString(10)}.png`);
              fs.writeFileSync(filePath, dataBuffer);
              await this.thermalPrinter.printImage(filePath);
              this.fileCreated.push(filePath);
            }
            break;

          case "line-separator":
            this.thermalPrinter.drawLine();
            break;

          case "line-feed":
            this.thermalPrinter.newLine();
            break;

          case "full-cut":
            this.thermalPrinter.cut();
            break;

          case "partial-cut":
          case "paper-cut":
            this.thermalPrinter.partialCut();
            break;

          case "cash-drawer":
            this.thermalPrinter.openCashDrawer();
            break;

          case "qr-code":
            const qrText = tag[tagName][0]["#text"] || "";
            const cellSize = tag[attrParentName]?.["@size"] || 8;
            this.thermalPrinter.printQR(qrText, { cellSize: parseInt(cellSize) });
            break;

          case "beep":
            this.thermalPrinter.beep();
            break;
        }
      } catch (error) {
        this.logger.log(`Tag işleme hatası (${tagName}): ${error.message}`);
      }
    }
  }

  setFontType(type) {
    switch (type) {
      case 'a':
        this.thermalPrinter.setTypeFontA();
        break;
      case 'b':
        this.thermalPrinter.setTypeFontB();
        break;
      default:
        this.thermalPrinter.setTypeFontA();
        break;
    }
  }



  setDirection(direction) {
    switch (direction) {
      case "center":
        this.thermalPrinter.alignCenter();
        break;
      case "left":
        this.thermalPrinter.alignLeft();
        break;
      case "right":
        this.thermalPrinter.alignRight();
        break;
    }
  }

  resetDirection() {
    this.thermalPrinter.alignLeft();
  }

  generateRandomString(length) {
    return Array.from({ length }, () =>
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(Math.random() * 62))
    ).join("");
  }

  async cleanupFiles() {
    try {
      for (const file of this.fileCreated) {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
          this.logger.log(`Geçici dosya silindi: ${file}`);
        }
      }
      this.fileCreated = []; // Listeyi temizle
    } catch (error) {
      this.logger.log(`Dosya temizleme hatası: ${error.message}`);
    }
  }

  async downloadImage(url) {
    try {
      this.logger.log(`Resim indirme başlıyor: ${url}`);

      // URL'nin geçerli olup olmadığını kontrol et
      if (!url || typeof url !== 'string') {
        this.logger.log(`Geçersiz URL: ${url}`);
        return false;
      }

      // URL'de extension yoksa .jpg ekle
      const fileName = `image-${this.generateRandomString(10)}`;
      let extension = path.extname(url);
      if (!extension) {
        extension = '.jpg';
        this.logger.log(`URL'de extension yok, .jpg ekleniyor: ${url}`);
      }

      let filePath = path.join(this.storagePath, `${fileName}${extension}`);

      this.logger.log(`Dosya yolu: ${filePath}`);

      // Axios ile resmi indir
      const response = await axios.get(url, {
        responseType: "stream",
        timeout: 30000, // 30 saniye timeout
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      this.logger.log(`HTTP Response Status: ${response.status}`);

      const imageStream = fs.createWriteStream(filePath);

      await new Promise((resolve, reject) => {
        response.data.pipe(imageStream)
          .on("error", (err) => {
            this.logger.log(`Stream hatası: ${err.message}`);
            reject(err);
          })
          .on("finish", () => {
            this.logger.log(`Dosya yazma tamamlandı: ${filePath}`);
            resolve(filePath);
          })
          .once("close", () => {
            this.logger.log(`Stream kapatıldı: ${filePath}`);
            resolve(filePath);
          });
      });

      // Dosyanın gerçekten oluştuğunu kontrol et
      if (!fs.existsSync(filePath)) {
        this.logger.log(`Dosya oluşturulamadı: ${filePath}`);
        return false;
      }

      const fileStats = fs.statSync(filePath);
      this.logger.log(`Dosya boyutu: ${fileStats.size} bytes`);

      if (fileStats.size === 0) {
        this.logger.log(`Dosya boş, siliniyor: ${filePath}`);
        fs.unlinkSync(filePath);
        return false;
      }

      // Eğer PNG değilse, PNG'ye dönüştür
      if (extension.toLowerCase() !== ".png") {
        try {
          const newPath = path.join(this.storagePath, `${fileName}.png`);
          this.logger.log(`PNG'ye dönüştürülüyor: ${filePath} -> ${newPath}`);

          const image = await jimp.read(filePath);
          await image.writeAsync(newPath);

          // Eski dosyayı sil
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }

          filePath = newPath;
          this.logger.log(`PNG dönüştürme başarılı: ${filePath}`);
        } catch (jimpError) {
          this.logger.log(`PNG dönüştürme hatası: ${jimpError.message}`);
          // PNG dönüştürme başarısız olursa orijinal dosyayı kullan
        }
      }

      // Resmi yazdır
      this.logger.log(`Resim yazdırılıyor: ${filePath}`);
      await this.thermalPrinter.printImage(filePath);

      // Temizleme listesine ekle
      this.fileCreated.push(filePath);

      this.logger.log(`Resim başarıyla indirildi ve yazdırıldı: ${filePath}`);
      return true;
    } catch (error) {
      this.logger.log(`Resim indirme ve yazdırma hatası: ${error.message}`);
      this.logger.log(`Hata detayı: ${error.stack}`);
      return false;
    }
  }

  async capture(url) {
    try {
      this.logger.log(`Web sayfası yakalanıyor: ${url}`);

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle2' });
      // Sayfanın tam yüklenmesi için bekle
      await new Promise(resolve => setTimeout(resolve, 2000));

      const screenshotPath = path.join(this.storagePath, `screenshot-${this.generateRandomString(10)}.png`);
      await page.screenshot({ path: screenshotPath, fullPage: true });

      await browser.close();

      // Ekran görüntüsünü yazdır
      await this.thermalPrinter.printImage(screenshotPath);

      // Temizleme listesine ekle
      this.fileCreated.push(screenshotPath);

      this.logger.log(`Web sayfası başarıyla yakalandı ve yazdırıldı: ${screenshotPath}`);
      return true;
    } catch (error) {
      this.logger.log(`Web sayfası yakalama hatası: ${error.message}`);
      return false;
    }
  }

  async renderTable(tableData) {
    try {
      const formattedRows = tableData.map(row => {
        const text = row.text ? (row.text[0]["#text"] || "") : "";
        return {
          text: text,
          align: (row.attributes && row.attributes["@align"]) || 'left',
          width: (row.attributes && row.attributes["@width"]) || 0.33,
          cols: (row.attributes && row.attributes["@cols"]) || undefined,
          bold: (row.attributes && row.attributes["@bold"] === "true") || false
        };
      });

      this.thermalPrinter.tableCustom(formattedRows);
      return true;
    } catch (error) {
      this.logger.log(`Tablo oluşturma hatası: ${error.message}`);
      return false;
    }
  }
}

module.exports = XMLParserTwo;
