const { app } = require('electron'); // Electron'un app modü<PERSON>ünü içe aktar
const fs = require('fs'); // Dosya sistemi modülünü içe aktar
const moment = require('moment'); // Moment.js kütüphanesini içe aktar

class Logger {
    constructor() {
        const appDataPath = app.getPath("appData");
        const currentDate = moment().format("YYYY-MM-DD");
        this.logFilePath = `${appDataPath}/sewpos/log-${currentDate}.txt`;
        this.ensureLogDirectory();
    }

    // Log dizininin varlığını kontrol et ve oluştur
    ensureLogDirectory() {
        const logDir = `${app.getPath("appData")}/sewpos`;
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }

    // Yeni log ekle
    log(text) {
        const separator = "=".repeat(50);
        const timestamp = moment().format("YYYY-MM-DD HH:mm:ss");
        const logText = `${timestamp} => ${text}`;

        if (!fs.existsSync(this.logFilePath)) {
            fs.writeFileSync(this.logFilePath, "");
        }

        const fileContent = fs.readFileSync(this.logFilePath, "utf8");
        fs.writeFileSync(
            this.logFilePath, 
            `${logText}\n${separator}\n${fileContent}`
        );
    }

    // Belirli bir tarihin loglarını getir
    getLogContent(date) {
        const logDate = date || moment().format("YYYY-MM-DD");
        const logFilePath = `${app.getPath("appData")}/sewpos/log-${logDate}.txt`;

        if (!fs.existsSync(logFilePath)) {
            return [];
        }

        const fileContent = fs.readFileSync(logFilePath, "utf8");
        return fileContent.split(`\n${"=".repeat(50)}\n`);
    }

    // Belirli bir tarihin loglarını temizle
    clear(date) {
        const logDate = date || moment().format("YYYY-MM-DD");
        const logFilePath = `${app.getPath("appData")}/sewpos/log-${logDate}.txt`;

        if (fs.existsSync(logFilePath)) {
            fs.writeFileSync(logFilePath, "");
        }
    }

    // Belirli bir tarih aralığındaki logları getir
    getLogsByDateRange(startDate, endDate) {
        const start = moment(startDate);
        const end = moment(endDate);
        const logs = [];

        for (let m = moment(start); m.diff(end, 'days') <= 0; m.add(1, 'days')) {
            const date = m.format('YYYY-MM-DD');
            const dateLogs = this.getLogContent(date);
            logs.push(...dateLogs.filter(log => log.trim()));
        }

        return logs;
    }

    // Son X günün loglarını getir
    getRecentLogs(days = 7) {
        const endDate = moment();
        const startDate = moment().subtract(days, 'days');
        return this.getLogsByDateRange(startDate, endDate);
    }

    // Log dosyasının boyutunu kontrol et
    getLogFileSize() {
        if (fs.existsSync(this.logFilePath)) {
            const stats = fs.statSync(this.logFilePath);
            return stats.size;
        }
        return 0;
    }

    // Büyük log dosyalarını arşivle
    archiveIfNeeded(maxSize = 5 * 1024 * 1024) { // 5MB varsayılan limit
        if (this.getLogFileSize() > maxSize) {
            const archivePath = this.logFilePath.replace('.txt', `-archive-${moment().unix()}.txt`);
            fs.copyFileSync(this.logFilePath, archivePath);
            fs.writeFileSync(this.logFilePath, ""); // Log dosyasını temizle
            return true;
        }
        return false;
    }
}

module.exports = Logger;
