{"name": "sewpos-printer", "version": "1.0.0", "description": "SewPos Yazıcı Yönetimi", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder --win"}, "build": {"appId": "com.sewpos.printer", "productName": "SewPOS Printer", "directories": {"output": "dist"}, "win": {"icon": "assets/icon.ico", "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "SewPOS Printer"}}, "author": "", "license": "ISC", "devDependencies": {"electron": "^24.1.0", "electron-builder": "^24.4.1"}, "dependencies": {"auto-launch": "^5.0.6", "axios": "^1.7.7", "electron-store": "^10.0.0", "fast-xml-parser": "^4.5.0", "moment": "^2.30.1", "node-thermal-printer": "^4.4.3", "socket.io-client": "^4.8.1", "@thiagoelg/node-printer": "^0.6.1", "puppeteer": "^19.8.0", "jimp": "^0.22.4", "xml2js": "^0.6.2"}}