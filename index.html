<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>HollyPos Yazıcı Yönetimi Kontrol</title>
  <style>
    /* <PERSON><PERSON> kodlarınızı buraya ekleyin */
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .status-panel {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    .status-item {
      margin: 10px 0;
      padding: 5px;
    }
    #loginForm, #printerControl {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    input {
      margin: 5px 0;
      padding: 5px;
      width: 200px;
    }
    button {
      margin: 10px 0;
      padding: 8px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    #status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
    }
    .user-info {
      background-color: #f8f9fa;
      padding: 10px 15px;
      border-radius: 5px;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .user-info-text {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    .store-name {
      font-size: 1.2em;
      font-weight: bold;
      color: #2c3e50;
    }
    .tenant-id {
      font-size: 0.9em;
      color: #7f8c8d;
    }
    .logout-button {
      background-color: #e74c3c;
      color: white;
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    .logout-button:hover {
      background-color: #c0392b;
    }
    .settings-panel {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    .setting-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 10px 0;
    }
    /* Switch stil */
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #4CAF50;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
  </style>
</head>
<body>
  <div id="loginForm">
    <h2>HollyPOS Yazıcı Yönetimi Kontrol - Giriş</h2>
    <div>
      <input type="text" id="username" placeholder="Kullanıcı adı" />
    </div>
    <div>
      <input type="password" id="password" placeholder="Şifre" />
    </div>
    <button id="loginButton">Giriş Yap</button>
  </div>

  <div id="printerControl" style="display: none;">
    <div class="user-info">
      <div class="user-info-text">
        <span id="storeName" class="store-name">Restoran Adı</span>
        <span id="tenantId" class="tenant-id">Tenant ID: -</span>
      </div>
      <button id="logoutButton" class="logout-button">Çıkış Yap</button>
    </div>

    <h2>Yazıcı Kontrol Paneli</h2>

    <div class="settings-panel">
      <h3>Ayarlar</h3>
      <div class="setting-item">
        <label class="switch">
          <input type="checkbox" id="autoLaunchToggle">
          <span class="slider"></span>
        </label>
        <span>Bilgisayar açıldığında otomatik başlat</span>
      </div>
    </div>
    
    <div class="status-panel">
      <h3>Sistem Durumu</h3>
      <div class="status-item" id="networkStatus">İnternet: Kontrol ediliyor...</div>
      <div class="status-item" id="socketStatus">Socket Bağlantısı: Kontrol ediliyor...</div>
      <div class="status-item" id="printerStatus">Yazıcı Durumu: Kontrol ediliyor...</div>
    </div>

    <button id="printButton">Bekleyen Siparişleri Kontrol Et</button>
    <div id="status"></div>
  </div>

  <script src="renderer.js"></script>
</body>
</html>
